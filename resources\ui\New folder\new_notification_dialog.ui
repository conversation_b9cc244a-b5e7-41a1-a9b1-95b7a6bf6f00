<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>Dialog</class>
 <widget class="QDialog" name="Dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1100</width>
    <height>824</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>1100</width>
    <height>824</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>1100</width>
    <height>824</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>New notification</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QDialog{
background-color:rgb(255,255,255);
}
</string>
  </property>
  <property name="sizeGripEnabled">
   <bool>false</bool>
  </property>
  <property name="modal">
   <bool>false</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QFrame" name="time_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="topMargin">
       <number>30</number>
      </property>
      <property name="bottomMargin">
       <number>20</number>
      </property>
      <item>
       <widget class="QLabel" name="time_label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <family>Traditional Arabic</family>
          <pointsize>30</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>00:00 PM</string>
        </property>
        <property name="scaledContents">
         <bool>false</bool>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="noti_kind_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
background-color: rgb(215, 215, 255);
background-color: rgb(193, 18, 51);
border-radius: 19px;
border: 1px solid  rgb(97, 99, 100);
}

QPushButton:hover{
background-color: rgb(180, 239, 255);
}

QPushButton:checked{
	background-color: rgb(8, 185, 123);
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="topMargin">
       <number>20</number>
      </property>
      <property name="rightMargin">
       <number>33</number>
      </property>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_4">
        <property name="font">
         <font>
          <family>Microsoft Uighur</family>
          <pointsize>24</pointsize>
         </font>
        </property>
        <property name="text">
         <string>مرة واحدة</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="once_noti">
        <property name="minimumSize">
         <size>
          <width>38</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>38</width>
          <height>38</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/newPrefix/images/button_88647.png</normaloff>:/newPrefix/images/button_88647.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>38</width>
          <height>38</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Maximum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>60</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_3">
        <property name="font">
         <font>
          <family>Microsoft Uighur</family>
          <pointsize>24</pointsize>
         </font>
        </property>
        <property name="text">
         <string>ثابت</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="permenante_noti">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>38</width>
          <height>38</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>38</width>
          <height>38</height>
         </size>
        </property>
        <property name="sizeIncrement">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="resources.qrc">
          <normaloff>:/newPrefix/images/button_88647.png</normaloff>:/newPrefix/images/button_88647.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>38</width>
          <height>38</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label">
        <property name="font">
         <font>
          <family>Microsoft Uighur</family>
          <pointsize>26</pointsize>
         </font>
        </property>
        <property name="text">
         <string>نوع الاشعار :</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="noti_settings_frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <item>
       <widget class="QFrame" name="date_frame">
        <property name="maximumSize">
         <size>
          <width>650</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_5">
         <item>
          <widget class="QCalendarWidget" name="calendarWidget">
           <property name="minimumSize">
            <size>
             <width>450</width>
             <height>450</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Monotype Hadassah</family>
             <pointsize>20</pointsize>
             <underline>false</underline>
             <strikeout>false</strikeout>
             <kerning>true</kerning>
            </font>
           </property>
           <property name="autoFillBackground">
            <bool>true</bool>
           </property>
           <property name="locale">
            <locale language="English" country="UnitedStates"/>
           </property>
           <property name="minimumDate">
            <date>
             <year>2024</year>
             <month>6</month>
             <day>17</day>
            </date>
           </property>
           <property name="maximumDate">
            <date>
             <year>2025</year>
             <month>12</month>
             <day>31</day>
            </date>
           </property>
           <property name="gridVisible">
            <bool>false</bool>
           </property>
           <property name="horizontalHeaderFormat">
            <enum>QCalendarWidget::SingleLetterDayNames</enum>
           </property>
           <property name="verticalHeaderFormat">
            <enum>QCalendarWidget::NoVerticalHeader</enum>
           </property>
           <property name="navigationBarVisible">
            <bool>true</bool>
           </property>
           <property name="dateEditEnabled">
            <bool>true</bool>
           </property>
           <property name="dateEditAcceptDelay">
            <number>1500</number>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="time_frame_2">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>600</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <property name="topMargin">
          <number>30</number>
         </property>
         <item>
          <widget class="QFrame" name="before_adan_frame">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_6">
            <property name="bottomMargin">
             <number>10</number>
            </property>
            <item>
             <widget class="QLabel" name="label_5">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Microsoft Uighur</family>
                <pointsize>26</pointsize>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::NoFrame</enum>
              </property>
              <property name="text">
               <string>الاختيار حسب وقت الصلاة :</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QFrame" name="adan_min_frame">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="frameShape">
               <enum>QFrame::StyledPanel</enum>
              </property>
              <property name="frameShadow">
               <enum>QFrame::Raised</enum>
              </property>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <property name="spacing">
                <number>10</number>
               </property>
               <item>
                <widget class="QSpinBox" name="minutes_spin">
                 <property name="minimumSize">
                  <size>
                   <width>200</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Microsoft Uighur</family>
                   <pointsize>20</pointsize>
                  </font>
                 </property>
                 <property name="frame">
                  <bool>false</bool>
                 </property>
                 <property name="keyboardTracking">
                  <bool>true</bool>
                 </property>
                 <property name="suffix">
                  <string> دقائق</string>
                 </property>
                 <property name="prefix">
                  <string>ب </string>
                 </property>
                 <property name="minimum">
                  <number>3</number>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="QComboBox" name="adan_box">
                 <property name="minimumSize">
                  <size>
                   <width>115</width>
                   <height>50</height>
                  </size>
                 </property>
                 <property name="font">
                  <font>
                   <family>Sakkal Majalla</family>
                   <pointsize>20</pointsize>
                  </font>
                 </property>
                 <property name="styleSheet">
                  <string notr="true">QComboBox {
                border: 1px solid gray;
                border-radius: 3px;
                padding: 1px 10px 1px 7px;
                min-width: 6em;
            }
            QComboBox::drop-down {
                
                width: 26px;
                border-left-width: 1px;
                border-left-color: darkgray;
                border-left-style: solid;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
                
	background-color: rgb(221, 221, 221);
            }
QComboBox::down-arrow {
				width:15px;
				height: 15px;
				margin: 1px 3px 1px 3px;
                image: url(:/newPrefix/images/down-arrow.png); /* Use the resource path */
}
           

           

</string>
                 </property>
                 <property name="frame">
                  <bool>true</bool>
                 </property>
                 <item>
                  <property name="text">
                   <string>قبل اذان الفجر </string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>قبل اذان الظهر </string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>قبل اذان العصر </string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>قبل اذان المغرب </string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>قبل اذان العشاء </string>
                  </property>
                 </item>
                 <item>
                  <property name="text">
                   <string>قبل صلاة الجمعة</string>
                  </property>
                 </item>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="time_select_frame">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="styleSheet">
            <string notr="true">QFrame{
background-color: rgb(240, 240, 240)
}</string>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_4">
            <property name="spacing">
             <number>17</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <property name="bottomMargin">
             <number>20</number>
            </property>
            <item>
             <widget class="QLabel" name="label_6">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Microsoft Uighur</family>
                <pointsize>26</pointsize>
               </font>
              </property>
              <property name="frameShape">
               <enum>QFrame::HLine</enum>
              </property>
              <property name="text">
               <string>الاختيار حسب الساعة :</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QTimeEdit" name="time_select">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <pointsize>20</pointsize>
               </font>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="readOnly">
               <bool>true</bool>
              </property>
              <property name="buttonSymbols">
               <enum>QAbstractSpinBox::UpDownArrows</enum>
              </property>
              <property name="displayFormat">
               <string>hh:mm </string>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QVBoxLayout" name="verticalLayout_7">
            <property name="spacing">
             <number>17</number>
            </property>
            <property name="topMargin">
             <number>20</number>
            </property>
            <item>
             <widget class="QLabel" name="label_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="font">
               <font>
                <family>Microsoft Uighur</family>
                <pointsize>26</pointsize>
               </font>
              </property>
              <property name="text">
               <string>مدة التشغيل (اختياري) :</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QSpinBox" name="spinBox">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>50</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Sakkal Majalla</family>
                <pointsize>20</pointsize>
               </font>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
              <property name="suffix">
               <string> دقيقة</string>
              </property>
              <property name="minimum">
               <number>0</number>
              </property>
              <property name="maximum">
               <number>100</number>
              </property>
              <property name="singleStep">
               <number>1</number>
              </property>
              <property name="value">
               <number>0</number>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
      <item alignment="Qt::AlignLeft">
       <widget class="QFrame" name="frame_2">
        <property name="styleSheet">
         <string notr="true">QPushButton{
background-color: rgb(212, 212, 212);
border-radius: 16px;
border: 1px solid  rgb(97, 99, 100);
}

QPushButton:hover{
background-color: rgb(180, 239, 255);
}

QPushButton:checked{
	
	background-color: rgb(143, 216, 255);
}</string>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_8">
         <property name="spacing">
          <number>120</number>
         </property>
         <property name="topMargin">
          <number>50</number>
         </property>
         <item>
          <widget class="QPushButton" name="pushButton_3">
           <property name="minimumSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/newPrefix/images/stop-button_73880.png</normaloff>:/newPrefix/images/stop-button_73880.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_2">
           <property name="minimumSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="resources.qrc">
             <normaloff>:/newPrefix/images/stop-button_73880.png</normaloff>:/newPrefix/images/stop-button_73880.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="save_sound_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
background-color: rgb(249, 249, 249);
border-radius: 5px;
}

QPushButton:hover{
border: 1px solid rgb(127, 127, 127);
}

#notification_sound_button:hover{
background-color: rgb(255, 240, 205);
}


#save_notification_button:hover{
background-color: rgb(71, 186, 119);
color: rgb(255,255,255);
}
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <item>
       <widget class="QPushButton" name="save_notification_button">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>50</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>50</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft Uighur</family>
          <pointsize>22</pointsize>
         </font>
        </property>
        <property name="text">
         <string>حفظ الاشعار</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="notification_sound_button">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>50</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>50</height>
         </size>
        </property>
        <property name="sizeIncrement">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="baseSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Microsoft Uighur</family>
          <pointsize>22</pointsize>
         </font>
        </property>
        <property name="text">
         <string>اختيار الصوت</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
