<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>NotificationInfo_dialog</class>
 <widget class="QDialog" name="NotificationInfo_dialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>350</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Maximum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>400</width>
    <height>350</height>
   </size>
  </property>
  <property name="sizeIncrement">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="cursor">
   <cursorShape>ArrowCursor</cursorShape>
  </property>
  <property name="windowTitle">
   <string>معلومات الاشعار</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../resource_file.qrc">
    <normaloff>:/newPrefix/images/mosque.png</normaloff>:/newPrefix/images/mosque.png</iconset>
  </property>
  <property name="styleSheet">
   <string notr="true">
QDialog{
background-color: rgb(255,255,255);
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="topMargin">
    <number>11</number>
   </property>
   <property name="bottomMargin">
    <number>15</number>
   </property>
   <item>
    <widget class="QLabel" name="noti_date_label">
     <property name="font">
      <font>
       <family>Monotype Hadassah</family>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="styleSheet">
      <string notr="true">color:black;</string>
     </property>
     <property name="text">
      <string>04/08/2024</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="info_frame">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QLabel" name="noti_time_label">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="font">
         <font>
          <family>Traditional Arabic</family>
          <pointsize>36</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color:black;</string>
        </property>
        <property name="text">
         <string>00:00:00</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignHCenter|Qt::AlignTop</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="noti_info_label">
        <property name="font">
         <font>
          <family>Traditional Arabic</family>
          <pointsize>26</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true">color:black;</string>
        </property>
        <property name="text">
         <string>قبل صلاة الظهر ب </string>
        </property>
        <property name="alignment">
         <set>Qt::AlignHCenter|Qt::AlignTop</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="edit_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
background-color: rgb(13, 13, 13);
color: rgb(255,255,255);
border: none;
border-radius: 20px;
}

QPushButton:hover{
border: 3px solid black;
background-color: rgb(255,255,255);
color: black;
}
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="topMargin">
       <number>11</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="edit_sound_noti_button">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <property name="sizeIncrement">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Traditional Arabic</family>
          <pointsize>16</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>تعديل الصوت</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="save_del_frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="styleSheet">
      <string notr="true">QPushButton{
color: rgb(255,255,255);
border-radius: 20px;
}

#delete_noti_button{
background-color: rgb(167, 25, 35);
}
#delete_noti_button:hover{
background-color: rgb(190, 28, 41);
}

#save_noti_button{
background-color: rgb(8, 148, 108);
}

#save_noti_button:hover{
background-color: rgb(8, 185, 123);
}</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>11</number>
      </property>
      <item>
       <widget class="QPushButton" name="save_noti_button">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Traditional Arabic</family>
          <pointsize>16</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>حفظ</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="delete_noti_button">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>40</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>40</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Traditional Arabic</family>
          <pointsize>16</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="text">
         <string>حذف الاشعار</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resource_file.qrc"/>
 </resources>
 <connections/>
</ui>
