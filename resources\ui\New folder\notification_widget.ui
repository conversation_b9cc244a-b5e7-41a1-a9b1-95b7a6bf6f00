<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>notification_widget</class>
 <widget class="QWidget" name="notification_widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>310</width>
    <height>360</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>310</width>
    <height>360</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>اشعار</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#notification_widget{
background-color: rgb(255,255,255);
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_3">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="notification_frame">
     <property name="minimumSize">
      <size>
       <width>300</width>
       <height>350</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>300</width>
       <height>350</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">#notification_frame{
background-color: rgb(255,255,255);
border: 3px solid rgb(13, 13, 13);
border-radius: 5px;
}
</string>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QFrame" name="frame">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <item>
          <widget class="QLabel" name="noti_date_label">
           <property name="font">
            <font>
             <family>Monotype Hadassah</family>
             <pointsize>10</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string>04/08/2024</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignRight|Qt::AlignTop|Qt::AlignTrailing</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="noti_time_label">
           <property name="font">
            <font>
             <family>Arial Black</family>
             <pointsize>24</pointsize>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="frameShape">
            <enum>QFrame::NoFrame</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Plain</enum>
           </property>
           <property name="text">
            <string>09:48 AM</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignHCenter|Qt::AlignTop</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="noti_adan_label">
           <property name="font">
            <font>
             <family>Arabic Typesetting</family>
             <pointsize>25</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true"/>
           </property>
           <property name="text">
            <string>&lt;html&gt;&lt;head/&gt;&lt;body&gt;&lt;p&gt;قبل صلاة الظهر &lt;br/&gt;ب 5 دقائق&lt;/p&gt;&lt;/body&gt;&lt;/html&gt;</string>
           </property>
           <property name="scaledContents">
            <bool>false</bool>
           </property>
           <property name="alignment">
            <set>Qt::AlignBottom|Qt::AlignHCenter</set>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QFrame" name="frame_2">
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_2">
            <property name="spacing">
             <number>9</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QLabel" name="is_noti_active_label">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>0</height>
               </size>
              </property>
              <property name="font">
               <font>
                <family>Arabic Typesetting</family>
                <pointsize>24</pointsize>
               </font>
              </property>
              <property name="text">
               <string>مفعل</string>
              </property>
              <property name="alignment">
               <set>Qt::AlignCenter</set>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QPushButton" name="activate_noti_button">
              <property name="minimumSize">
               <size>
                <width>34</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>34</width>
                <height>34</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true">QPushButton{
background-color: rgb(193, 18, 51);
border:none;
border-radius: 17px;
}

QPushButton:hover{
background-color: rgb(180, 239, 255);
}
QPushButton:checked{
background-color: rgb(8, 185, 123);
}</string>
              </property>
              <property name="text">
               <string/>
              </property>
              <property name="icon">
               <iconset resource="resources.qrc">
                <normaloff>:/newPrefix/images/power_3039519.png</normaloff>
                <normalon>:/newPrefix/images/button_88647.png</normalon>:/newPrefix/images/power_3039519.png</iconset>
              </property>
              <property name="iconSize">
               <size>
                <width>34</width>
                <height>34</height>
               </size>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
              <property name="checked">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QFrame" name="frame_3">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QPushButton" name="show_noti_button">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>60</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Arabic Typesetting</family>
             <pointsize>24</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
background-color: rgb(13, 13, 13);
color: rgb(255,255,255);
border-bottom-left-radius: 5px;
border-bottom-right-radius: 5px;
}

QPushButton:hover{
	background-color: rgb(236, 236, 236);
	color:black;
border-top: 2px solid rgb(13, 13, 13);
}</string>
           </property>
           <property name="text">
            <string>عرض الاشعار</string>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="show_noti_resources.qrc"/>
  <include location="resources.qrc"/>
 </resources>
 <connections/>
</ui>
