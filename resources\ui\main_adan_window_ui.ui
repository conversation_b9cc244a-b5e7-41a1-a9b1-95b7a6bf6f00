<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>central_widget</class>
 <widget class="QWidget" name="central_widget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1397</width>
    <height>1199</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#central_widget{
background-color: rgb(255, 255, 255);

}
</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="expandedMenu" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>180</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>220</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true">#expandedMenu{
border-right: 1px solid black;
background-color: rgb(38, 38, 38);
color: rgb(255,255,255);
}
	
#menuTitleLabel{
border-bottom: 5px solid rgb(255,255,255);
color: rgb(255,255,255);

}

QPushButton{
padding: 12px 0 12px;
border:none;
color: rgb(255,255,255);
}

QPushButton:checked{

	background-color: rgb(53, 53, 53);
font-weight: bold;
}
QPushButton:hover{

background-color: rgb(56, 56, 56);
font-weight: bold;
}


</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>11</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>22</number>
      </property>
      <item>
       <widget class="QLabel" name="menuTitleLabel">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>49</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>50</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Calibri</family>
          <pointsize>19</pointsize>
          <weight>75</weight>
          <bold>true</bold>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>القائمة</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignCenter</set>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="mainPageButton">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>54</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Calibri</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string>الصفحة الرئيسية</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
        <property name="autoExclusive">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="notificationsPageButton">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>54</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Calibri</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string>الاشعارات</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="quraanPageButton">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>54</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Calibri</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string>القرءان</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QPushButton" name="settingsPageButton">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>54</height>
         </size>
        </property>
        <property name="font">
         <font>
          <family>Calibri</family>
          <pointsize>16</pointsize>
         </font>
        </property>
        <property name="text">
         <string>الاعدادات</string>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>500</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QFrame" name="frame_20">
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>80</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_17">
         <item>
          <widget class="QPushButton" name="powerOffButton">
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>50</width>
             <height>50</height>
            </size>
           </property>
           <property name="font">
            <font>
             <family>Calibri</family>
             <pointsize>16</pointsize>
            </font>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton{
padding: 12px;
border:none;
border-radius: 25px;
}

QPushButton:hover{
border-radius: 25px;
background-color: rgb(56,56,56);
}</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../resource_file.qrc">
             <normaloff>:/newPrefix/images/undo.png</normaloff>:/newPrefix/images/undo.png</iconset>
           </property>
           <property name="iconSize">
            <size>
             <width>30</width>
             <height>30</height>
            </size>
           </property>
           <property name="checkable">
            <bool>true</bool>
           </property>
           <property name="flat">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QFrame" name="frame_19">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>70</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>70</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_19">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>15</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="openMenuButton">
        <property name="minimumSize">
         <size>
          <width>50</width>
          <height>50</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>50</width>
          <height>50</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QPushButton{
background-color: rgb(255, 255, 255);
border: none;
}

QPushButton:hover{
border: 1px solid black;
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="icon">
         <iconset resource="../resource_file.qrc">
          <normaloff>:/newPrefix/images/menu_12237096.png</normaloff>:/newPrefix/images/menu_12237096.png</iconset>
        </property>
        <property name="iconSize">
         <size>
          <width>30</width>
          <height>30</height>
         </size>
        </property>
        <property name="checkable">
         <bool>true</bool>
        </property>
        <property name="default">
         <bool>false</bool>
        </property>
        <property name="flat">
         <bool>false</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_4">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>40</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="mouseTracking">
      <bool>false</bool>
     </property>
     <property name="tabletTracking">
      <bool>false</bool>
     </property>
     <property name="contextMenuPolicy">
      <enum>Qt::DefaultContextMenu</enum>
     </property>
     <property name="acceptDrops">
      <bool>false</bool>
     </property>
     <property name="styleSheet">
      <string notr="true">#stackedWidget{
background-color: rgb(0,0,0);
margin-right:30px;
}

</string>
     </property>
     <property name="currentIndex">
      <number>3</number>
     </property>
     <widget class="QWidget" name="mainPage">
      <property name="styleSheet">
       <string notr="true">

#mainPage{
background-color: rgb(255, 255, 255);
}</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_4">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>16</number>
       </property>
       <property name="rightMargin">
        <number>25</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QWidget" name="masgedNameWidget" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Minimum">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>100</height>
          </size>
         </property>
         <property name="font">
          <font>
           <pointsize>8</pointsize>
          </font>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="spacing">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>11</number>
          </property>
          <item>
           <spacer name="horizontalSpacer_3">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>216</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
          <item>
           <widget class="QLabel" name="masgedNameLabel">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="font">
             <font>
              <family>Microsoft Uighur</family>
              <pointsize>35</pointsize>
              <weight>50</weight>
              <bold>false</bold>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color: black;</string>
            </property>
            <property name="text">
             <string>مسجد صلاح الدين الايوبي - معاوية</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
            <property name="wordWrap">
             <bool>false</bool>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_4">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>216</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_2" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>40</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QFrame" name="main_frame">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>15</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="QWidget" name="timeDateWidget" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>1</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>380</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true">#jomoaaWidget, #shorokWidget {
	border-radius: 25px;
	border: 4px solid black;
background-color: rgb(255, 255, 255);

}

</string>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_6">
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <spacer name="horizontalSpacer_13">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QFrame" name="frame_12">
               <property name="maximumSize">
                <size>
                 <width>202</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_29">
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QFrame" name="frame_21">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>100</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="jomoaaWidget" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>150</width>
                    <height>130</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>200</width>
                    <height>160</height>
                   </size>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_11">
                   <property name="spacing">
                    <number>7</number>
                   </property>
                   <property name="leftMargin">
                    <number>11</number>
                   </property>
                   <property name="rightMargin">
                    <number>11</number>
                   </property>
                   <item>
                    <widget class="QLabel" name="jomoaa_label">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft Uighur</family>
                       <pointsize>32</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>الجمعة</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="jomoaa_adan_time">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <family>Bahnschrift</family>
                       <pointsize>19</pointsize>
                       <weight>50</weight>
                       <bold>false</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>00:00 AM</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_15">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Fixed</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item alignment="Qt::AlignHCenter">
              <widget class="QWidget" name="timeWidget" native="true">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>1</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>560</width>
                 <height>200</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>330</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">#timeWidget{
border: 4px solid rgb(0, 0, 0);
border-radius: 25px;
}</string>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_12">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>5</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QFrame" name="time_upper_frame">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>500</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QWidget{
	color: rgb(120, 208, 62);
}</string>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_34">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>5</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QFrame" name="frame_36">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="clockLabel">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Maximum" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="sizeIncrement">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="baseSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Bahnschrift</family>
                       <pointsize>80</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="text">
                      <string>07:20:58</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                     <property name="wordWrap">
                      <bool>false</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QFrame" name="am_pm_frame">
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_36">
                      <property name="spacing">
                       <number>0</number>
                      </property>
                      <property name="leftMargin">
                       <number>0</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="rightMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item>
                       <widget class="QFrame" name="frame_35">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QLabel" name="seconds_label">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                        <property name="alignment">
                         <set>Qt::AlignCenter</set>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QLabel" name="am_pm_label">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="font">
                         <font>
                          <family>Bahnschrift</family>
                          <pointsize>36</pointsize>
                          <weight>50</weight>
                          <bold>false</bold>
                         </font>
                        </property>
                        <property name="text">
                         <string>AM</string>
                        </property>
                        <property name="alignment">
                         <set>Qt::AlignCenter</set>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="frame_34">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                   <item>
                    <widget class="QFrame" name="frame_37">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="timeLowerWidget" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_7">
                   <property name="spacing">
                    <number>15</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <spacer name="horizontalSpacer_21">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Expanding</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>0</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item alignment="Qt::AlignTop">
                    <widget class="QLabel" name="dateLabel">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <family>Sakkal Majalla</family>
                       <pointsize>32</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>00/00/0000</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignHCenter|Qt::AlignTop</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_24">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>0</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item alignment="Qt::AlignTop">
                    <widget class="QLabel" name="dayLabel">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <family>Sakkal Majalla</family>
                       <pointsize>32</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>الجمعة</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_16">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Fixed</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QFrame" name="frame_13">
               <property name="maximumSize">
                <size>
                 <width>202</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_30">
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QFrame" name="frame_18">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>100</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="shorokWidget" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>150</width>
                    <height>130</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>200</width>
                    <height>160</height>
                   </size>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_10">
                   <property name="spacing">
                    <number>7</number>
                   </property>
                   <property name="leftMargin">
                    <number>11</number>
                   </property>
                   <property name="topMargin">
                    <number>11</number>
                   </property>
                   <property name="rightMargin">
                    <number>11</number>
                   </property>
                   <item>
                    <widget class="QLabel" name="shorok_label">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft Uighur</family>
                       <pointsize>32</pointsize>
                       <weight>75</weight>
                       <bold>true</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>الشروق</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QLabel" name="shorok_adan_time">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <family>Bahnschrift</family>
                       <pointsize>19</pointsize>
                       <weight>50</weight>
                       <bold>false</bold>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>00:00 AM</string>
                     </property>
                     <property name="alignment">
                      <set>Qt::AlignCenter</set>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_14">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QFrame" name="frame_30">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
              <horstretch>0</horstretch>
              <verstretch>1</verstretch>
             </sizepolicy>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>50</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="remainingTimeWidget" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <property name="spacing">
              <number>10</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <spacer name="horizontalSpacer_5">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="remainingTimeLabel">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>Consolas</family>
                 <pointsize>45</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QWidget{
color: rgb(193, 18, 51);
}</string>
               </property>
               <property name="text">
                <string>00:00:00</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QWidget" name="nextAdanWidget" native="true">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_13">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>11</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <item alignment="Qt::AlignHCenter">
                 <widget class="QLabel" name="remaining_text_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Calibri</family>
                    <pointsize>28</pointsize>
                    <weight>50</weight>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QWidget{
color: rgb(193, 18, 51);
}</string>
                  </property>
                  <property name="text">
                   <string>الوقت المتبقي لأذان</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="nextAdanNameLabel">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Calibri</family>
                    <pointsize>30</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QWidget{
color: rgb(8, 148, 108);}
</string>
                  </property>
                  <property name="text">
                   <string>المغرب</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_6">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="emergency_stop_widget" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>0</verstretch>
             </sizepolicy>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_27">
             <property name="spacing">
              <number>15</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <spacer name="horizontalSpacer_25">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QPushButton" name="emergency_stop_button">
               <property name="minimumSize">
                <size>
                 <width>46</width>
                 <height>46</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>46</width>
                 <height>46</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
background-color:rgb(213, 33, 10);
border-radius: 23px;
border: none;
}

QPushButton:hover{
background-color: rgb(200, 28, 9);
}

QPushButton:checked{
	background-color: rgb(8, 185, 123);
	background-color: rgb(6, 235, 102);
}</string>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="icon">
                <iconset resource="../resource_file.qrc">
                 <normaloff>:/newPrefix/images/power_3039519.png</normaloff>:/newPrefix/images/power_3039519.png</iconset>
               </property>
               <property name="iconSize">
                <size>
                 <width>46</width>
                 <height>46</height>
                </size>
               </property>
               <property name="checkable">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="emergency_label">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="font">
                <font>
                 <family>Calibri</family>
                 <pointsize>22</pointsize>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QWidget{
color: rgb(193, 18, 51);
}</string>
               </property>
               <property name="text">
                <string>لإيقاف الأذان إضغط هنا</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_26">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="adansWidget" native="true">
            <property name="sizePolicy">
             <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
              <horstretch>0</horstretch>
              <verstretch>1</verstretch>
             </sizepolicy>
            </property>
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <property name="spacing">
              <number>10</number>
             </property>
             <property name="leftMargin">
              <number>11</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>11</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QFrame" name="adans_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>560</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true"/>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_35">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QWidget" name="widget" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>480</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <layout class="QVBoxLayout" name="verticalLayout_21">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QFrame" name="frame_14">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">
#aserWidget, #dohorWidget, #fajerWidget, #ishaaWidget, #magrebWidget{
border-radius: 14px;
border: 4px solid black;
	background-color: rgb(255, 255, 255);
}

QPushButton{
border-bottom-left-radius: 14px;
border-bottom-right-radius: 14px;
border: 4px solid black;
border-top: none;
padding: 5px 0;
background-color: rgb(167, 25, 35);
color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(8, 185, 123);
}

QPushButton:checked{
background-color: rgb(8, 148, 108);
}</string>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QHBoxLayout" name="horizontalLayout_28">
                      <property name="leftMargin">
                       <number>0</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="rightMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item>
                       <widget class="QFrame" name="frame_43">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>0</height>
                         </size>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::StyledPanel</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_41">
                         <property name="spacing">
                          <number>5</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QWidget" name="ishaaWidget" native="true">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>0</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>250</width>
                             <height>200</height>
                            </size>
                           </property>
                           <layout class="QVBoxLayout" name="verticalLayout_9">
                            <property name="leftMargin">
                             <number>0</number>
                            </property>
                            <property name="rightMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="ishaa_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>39</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>العشاء</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLabel" name="ishaa_adan_time">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Bahnschrift</family>
                                <pointsize>24</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>00:00 AM</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="ishaa_activate_button">
                              <property name="font">
                               <font>
                                <family>Calibri</family>
                                <pointsize>15</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="text">
                               <string>تفعيل الاذان</string>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSlider" name="ishaa_volume_slider">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 2px; /* Set the line thickness */
    background: #d9d9d9;
    margin: 0px 0;
}

QSlider::handle:horizontal {
    background: black;
    border: 1px solid #5c5c5c;
    width: 16px;
    height: 16px;
    margin: -7px 0; /* Adjusted to vertically center the handle on the thinner groove */
    border-radius: 8px; /* Circular handle */
}

QSlider::add-page:horizontal {
    background: #d9d9d9;
}

QSlider::sub-page:horizontal {
    background: black;
}
</string>
                           </property>
                           <property name="maximum">
                            <number>100</number>
                           </property>
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                           <property name="invertedAppearance">
                            <bool>false</bool>
                           </property>
                           <property name="invertedControls">
                            <bool>false</bool>
                           </property>
                           <property name="tickPosition">
                            <enum>QSlider::NoTicks</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="ishaaSoundButton">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>22</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">QPushButton{
border-radius: 10px;
color: black;
border: 3px solid black;
background-color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(53, 53, 53);
color:white;
}

</string>
                           </property>
                           <property name="text">
                            <string>صوت اذان العشاء</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="frame_42">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>0</height>
                         </size>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::StyledPanel</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_40">
                         <property name="spacing">
                          <number>5</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QWidget" name="magrebWidget" native="true">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>0</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>250</width>
                             <height>200</height>
                            </size>
                           </property>
                           <property name="styleSheet">
                            <string notr="true"/>
                           </property>
                           <layout class="QVBoxLayout" name="verticalLayout_8">
                            <property name="leftMargin">
                             <number>0</number>
                            </property>
                            <property name="rightMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="magreb_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>39</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>المغرب</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLabel" name="magreb_adan_time">
                              <property name="font">
                               <font>
                                <family>Bahnschrift</family>
                                <pointsize>24</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>00:00 AM</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="magreb_activate_button">
                              <property name="font">
                               <font>
                                <family>Calibri</family>
                                <pointsize>15</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="text">
                               <string>تفعيل الاذان</string>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSlider" name="magreb_volume_slider">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 2px; /* Set the line thickness */
    background: #d9d9d9;
    margin: 0px 0;
}

QSlider::handle:horizontal {
    background: black;
    border: 1px solid #5c5c5c;
    width: 16px;
    height: 16px;
    margin: -7px 0; /* Adjusted to vertically center the handle on the thinner groove */
    border-radius: 8px; /* Circular handle */
}

QSlider::add-page:horizontal {
    background: #d9d9d9;
}

QSlider::sub-page:horizontal {
    background: black;
}
</string>
                           </property>
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="magrebSoundButton">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>22</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">QPushButton{
border-radius: 10px;
color: black;
border: 3px solid black;
background-color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(53, 53, 53);
color:white;
}

</string>
                           </property>
                           <property name="text">
                            <string>صوت اذان المغرب</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="frame_41">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>0</height>
                         </size>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::StyledPanel</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_39">
                         <property name="spacing">
                          <number>5</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QWidget" name="aserWidget" native="true">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>0</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>250</width>
                             <height>200</height>
                            </size>
                           </property>
                           <layout class="QVBoxLayout" name="verticalLayout_7">
                            <property name="leftMargin">
                             <number>0</number>
                            </property>
                            <property name="rightMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="aser_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>39</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>العصر</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLabel" name="aser_adan_time">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Bahnschrift</family>
                                <pointsize>24</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>00:00 AM</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="aser_activate_button">
                              <property name="font">
                               <font>
                                <family>Calibri</family>
                                <pointsize>15</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="text">
                               <string>تفعيل الاذان</string>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSlider" name="aser_volume_slider">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 2px; /* Set the line thickness */
    background: #d9d9d9;
    margin: 0px 0;
}

QSlider::handle:horizontal {
    background: black;
    border: 1px solid #5c5c5c;
    width: 16px;
    height: 16px;
    margin: -7px 0; /* Adjusted to vertically center the handle on the thinner groove */
    border-radius: 8px; /* Circular handle */
}

QSlider::add-page:horizontal {
    background: #d9d9d9;
}

QSlider::sub-page:horizontal {
    background: black;
}
</string>
                           </property>
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="aserSoundButton">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>22</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">QPushButton{
border-radius: 10px;
color: black;
border: 3px solid black;
background-color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(53, 53, 53);
color:white;
}

</string>
                           </property>
                           <property name="text">
                            <string>صوت اذان العصر</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="frame_29">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>0</height>
                         </size>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::StyledPanel</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_37">
                         <property name="spacing">
                          <number>5</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QWidget" name="dohorWidget" native="true">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>0</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>250</width>
                             <height>200</height>
                            </size>
                           </property>
                           <layout class="QVBoxLayout" name="verticalLayout_6">
                            <property name="leftMargin">
                             <number>0</number>
                            </property>
                            <property name="rightMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="dohor_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>39</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>الظهر</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLabel" name="dohor_adan_time">
                              <property name="font">
                               <font>
                                <family>Bahnschrift</family>
                                <pointsize>24</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>00:00 AM</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="dohor_activate_button">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Calibri</family>
                                <pointsize>15</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true"/>
                              </property>
                              <property name="text">
                               <string>تفعيل الاذان</string>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                              <property name="autoDefault">
                               <bool>false</bool>
                              </property>
                              <property name="default">
                               <bool>false</bool>
                              </property>
                              <property name="flat">
                               <bool>false</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSlider" name="dohor_volume_slider">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 2px; /* Set the line thickness */
    background: #d9d9d9;
    margin: 0px 0;
}

QSlider::handle:horizontal {
    background: black;
    border: 1px solid #5c5c5c;
    width: 16px;
    height: 16px;
    margin: -7px 0; /* Adjusted to vertically center the handle on the thinner groove */
    border-radius: 8px; /* Circular handle */
}

QSlider::add-page:horizontal {
    background: #d9d9d9;
}

QSlider::sub-page:horizontal {
    background: black;
}
</string>
                           </property>
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="dohorSoundButton">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>22</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">QPushButton{
border-radius: 10px;
color: black;
border: 3px solid black;
background-color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(53, 53, 53);
color:white;
}

</string>
                           </property>
                           <property name="text">
                            <string>صوت اذان الظهر</string>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="frame_15">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>0</height>
                         </size>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::StyledPanel</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_33">
                         <property name="spacing">
                          <number>5</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QWidget" name="fajerWidget" native="true">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>0</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>250</width>
                             <height>200</height>
                            </size>
                           </property>
                           <layout class="QVBoxLayout" name="verticalLayout_5">
                            <property name="spacing">
                             <number>7</number>
                            </property>
                            <property name="leftMargin">
                             <number>0</number>
                            </property>
                            <property name="topMargin">
                             <number>11</number>
                            </property>
                            <property name="rightMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="fajer_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>39</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                                <kerning>true</kerning>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>الفجر</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QLabel" name="fajer_adan_time">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Bahnschrift</family>
                                <pointsize>24</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="text">
                               <string>00:00 AM</string>
                              </property>
                              <property name="alignment">
                               <set>Qt::AlignCenter</set>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="fajer_activate_button">
                              <property name="font">
                               <font>
                                <family>Calibri</family>
                                <pointsize>15</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="text">
                               <string>تفعيل الاذان</string>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSlider" name="fajer_volume_slider">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 2px; /* Set the line thickness */
    background: #d9d9d9;
    margin: 0px 0;
}

QSlider::handle:horizontal {
    background: black;
    border: 1px solid #5c5c5c;
    width: 16px;
    height: 16px;
    margin: -7px 0; /* Adjusted to vertically center the handle on the thinner groove */
    border-radius: 8px; /* Circular handle */
}

QSlider::add-page:horizontal {
    background: #d9d9d9;
}

QSlider::sub-page:horizontal {
    background: black;
}
</string>
                           </property>
                           <property name="orientation">
                            <enum>Qt::Horizontal</enum>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QPushButton" name="fajerSoundButton">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>0</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>16777215</width>
                             <height>16777215</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>22</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">QPushButton{
border-radius: 10px;
color: black;
border: 3px solid black;
background-color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(53, 53, 53);
color:white;
}

</string>
                           </property>
                           <property name="text">
                            <string>صوت اذان الفجر</string>
                           </property>
                           <property name="checkable">
                            <bool>true</bool>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="adanSoundWidget" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>74</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton{
background-color: rgb(255,255,255);
border:none;
}

#adanSoundWidget{
border: 3px solid black;

}

</string>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_3">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="QFrame" name="frame_16">
                     <property name="styleSheet">
                      <string notr="true">QPushButton{
qproperty-iconSize: 40px 40px;
}
QPushButton:hover {
    border-bottom: 2px solid rgb(53,53,53);
}
</string>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QHBoxLayout" name="horizontalLayout_30">
                      <item>
                       <widget class="QSlider" name="instant_play_volume_controller">
                        <property name="minimumSize">
                         <size>
                          <width>0</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>500</width>
                          <height>16777215</height>
                         </size>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">
QSlider::groove:horizontal {
    border: 1px solid #999999;
    height: 2px; /* Set the line thickness */
    background: #d9d9d9;
    margin: 0px 0;
}

QSlider::handle:horizontal {
    background: black;
    border: 1px solid #5c5c5c;
    width: 16px;
    height: 16px;
    margin: -7px 0; /* Adjusted to vertically center the handle on the thinner groove */
    border-radius: 8px; /* Circular handle */
}

QSlider::add-page:horizontal {
    background: #d9d9d9;
}

QSlider::sub-page:horizontal {
    background: black;
}
</string>
                        </property>
                        <property name="maximum">
                         <number>100</number>
                        </property>
                        <property name="pageStep">
                         <number>1</number>
                        </property>
                        <property name="value">
                         <number>0</number>
                        </property>
                        <property name="sliderPosition">
                         <number>0</number>
                        </property>
                        <property name="orientation">
                         <enum>Qt::Horizontal</enum>
                        </property>
                        <property name="tickPosition">
                         <enum>QSlider::NoTicks</enum>
                        </property>
                        <property name="tickInterval">
                         <number>1</number>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="instant_player_play_button">
                        <property name="minimumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                        <property name="icon">
                         <iconset resource="../resource_file.qrc">
                          <normaloff>:/newPrefix/images/play_14441317.png</normaloff>
                          <disabledoff>:/newPrefix/images/play_14441317.png</disabledoff>:/newPrefix/images/play_14441317.png</iconset>
                        </property>
                        <property name="iconSize">
                         <size>
                          <width>40</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="checkable">
                         <bool>true</bool>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="instant_player_pause_button">
                        <property name="minimumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                        <property name="icon">
                         <iconset resource="../resource_file.qrc">
                          <normaloff>:/newPrefix/images/square_13738097.png</normaloff>:/newPrefix/images/square_13738097.png</iconset>
                        </property>
                        <property name="iconSize">
                         <size>
                          <width>40</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="checkable">
                         <bool>false</bool>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="instant_player_resume_button">
                        <property name="minimumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                        <property name="icon">
                         <iconset resource="../resource_file.qrc">
                          <normaloff>:/newPrefix/images/pause_6415668.png</normaloff>:/newPrefix/images/pause_6415668.png</iconset>
                        </property>
                        <property name="iconSize">
                         <size>
                          <width>40</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="checkable">
                         <bool>true</bool>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="instant_player_stop_button">
                        <property name="minimumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="maximumSize">
                         <size>
                          <width>50</width>
                          <height>50</height>
                         </size>
                        </property>
                        <property name="text">
                         <string/>
                        </property>
                        <property name="icon">
                         <iconset resource="../resource_file.qrc">
                          <normaloff>:/newPrefix/images/stop.png</normaloff>:/newPrefix/images/stop.png</iconset>
                        </property>
                        <property name="iconSize">
                         <size>
                          <width>40</width>
                          <height>40</height>
                         </size>
                        </property>
                        <property name="checkable">
                         <bool>true</bool>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                   <item>
                    <widget class="QWidget" name="widget_11" native="true">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="font">
                      <font>
                       <pointsize>8</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true"/>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_31">
                      <property name="spacing">
                       <number>0</number>
                      </property>
                      <property name="leftMargin">
                       <number>10</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="rightMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item>
                       <widget class="QPushButton" name="instant_player_choose_file_button">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="font">
                         <font>
                          <family>Calibri</family>
                          <pointsize>15</pointsize>
                          <weight>75</weight>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">QPushButton{
border: 3px solid black;
border-bottom: 1px solid black;
padding: 0px 5px;
background-color: rgb(120, 208, 62);
}

QPushButton:hover{
background-color: rgb(130, 225, 67);
}</string>
                        </property>
                        <property name="text">
                         <string>ملف صوت للتشغيل الفوري</string>
                        </property>
                        <property name="checkable">
                         <bool>true</bool>
                        </property>
                       </widget>
                      </item>
                      <item>
                       <widget class="QPushButton" name="instant_player_delete_file_button">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="font">
                         <font>
                          <family>Calibri</family>
                          <pointsize>15</pointsize>
                          <weight>75</weight>
                          <bold>true</bold>
                         </font>
                        </property>
                        <property name="styleSheet">
                         <string notr="true">QPushButton{
border: 3px solid black;
border-top: 1px solid black;
padding: 0px 5px;
background-color:rgb(213, 33, 10);
color: rgb(255,255,255);
}

QPushButton:hover{
background-color: rgb(235, 33, 11);
}</string>
                        </property>
                        <property name="text">
                         <string>حذف الملف</string>
                        </property>
                        <property name="checkable">
                         <bool>true</bool>
                        </property>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </widget>
          </item>
          <item>
           <spacer name="verticalSpacer_7">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>10</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="notifications_page">
      <property name="styleSheet">
       <string notr="true">#notifications_page{
background-color: rgb(255,255,255);
}</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_16">
       <property name="leftMargin">
        <number>30</number>
       </property>
       <property name="rightMargin">
        <number>25</number>
       </property>
       <property name="bottomMargin">
        <number>30</number>
       </property>
       <item>
        <widget class="QFrame" name="frame">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_18">
          <item>
           <widget class="QFrame" name="notifications_frame">
            <property name="cursor">
             <cursorShape>ArrowCursor</cursorShape>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="inputMethodHints">
             <set>Qt::ImhHiddenText</set>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_17">
             <property name="spacing">
              <number>15</number>
             </property>
             <item>
              <widget class="QFrame" name="frame_28">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>180</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="total_noti_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="styleSheet">
                <string notr="true">color:black;</string>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_14">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="total_noti_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>30</pointsize>
                    <weight>50</weight>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::RightToLeft</enum>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Plain</enum>
                  </property>
                  <property name="text">
                   <string>0</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="total_noti_text_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Maximum" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>34</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>اجمالي عدد الاشعارات : </string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_24">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>150</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="noti_sort_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_15">
                <property name="spacing">
                 <number>12</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <spacer name="horizontalSpacer_12">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>500</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QPushButton" name="new_notification_buttton">
                  <property name="minimumSize">
                   <size>
                    <width>150</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>150</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>19</pointsize>
                    <weight>50</weight>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton{
background-color: rgb(38, 38, 38);
border-radius: 25px;
color:white;
}

QPushButton:hover{
background-color: rgb(255, 255, 255);
color:black;
border:2px solid black;
}
</string>
                  </property>
                  <property name="text">
                   <string>اشعار جديد</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QComboBox" name="noti_sort_box">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Maximum" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>119</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>19</pointsize>
                    <weight>50</weight>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QComboBox {
                border: 3px solid rgb(38, 38, 38);
				border-radius: 3px;
                padding: 1px 10px 1px 7px;
                min-width: 6em;
            }
QComboBox::drop-down {
                color:white;
                width: 26px;
                border-left-width: 1px;
                border-left-color: rgb(38, 38, 38);
                border-left-style: solid;

               background-color: rgb(38,38,38);
}
QComboBox::down-arrow {

				width:20px;
				height: 20px;
				margin: 1px 3px 1px 3px;
                image: url(:/newPrefix/images/down-chevron.png); /* Use the resource path */
}

QComboBox QAbstractItemView {
    selection-background-color: rgb(53,53,53); 
}
QComboBox QAbstractItemView::item:hover {
    background-color:rgb(53,53,53);

}
    

           

</string>
                  </property>
                  <property name="editable">
                   <bool>false</bool>
                  </property>
                  <property name="currentIndex">
                   <number>6</number>
                  </property>
                  <property name="duplicatesEnabled">
                   <bool>false</bool>
                  </property>
                  <property name="frame">
                   <bool>false</bool>
                  </property>
                  <item>
                   <property name="text">
                    <string>صلاة الفجر </string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>صلاة الظهر </string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>صلاة العصر </string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>صلاة المغرب </string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>صلاة العشاء </string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>صلاة الجمعة</string>
                   </property>
                  </item>
                  <item>
                   <property name="text">
                    <string>الكل </string>
                   </property>
                  </item>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="notis_text_label">
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>28</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>الاشعارات : </string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="noti_show_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>420</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>420</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">#noti_show_frame{
border: 2px solid rgb(53,53,53);
}</string>
               </property>
               <property name="frameShape">
                <enum>QFrame::StyledPanel</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_18">
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>20</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QScrollArea" name="display_noti_widget">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="sizeIncrement">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <kerning>true</kerning>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::LeftToRight</enum>
                  </property>
                  <property name="autoFillBackground">
                   <bool>false</bool>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QWidget {
                background-color: rgb(255, 255, 255);
}

QScrollBar{
padding: 20px 15px;
}

QScrollBar:horizontal {
                background-color: rgb(56, 56, 56);
                border: 1px solid rgb(56,56,56);
                height: 15px;
                margin: 0px ;
}

QScrollBar::handle:horizontal {
                background-color: rgb(100, 100, 100);
				border: 1px solid rgb(100,100,100);
                min-width: 20px;
				min-height: 30px;
				margin: 0px 16px 0px 16px;
}

            QScrollBar::handle:horizontal:hover {
                background-color: rgb(160,160, 160);
            }

            QScrollBar::handle:horizontal:pressed {
                background-color: rgb(165, 165, 165);
            }

            

            QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {
                background: none;
            }</string>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Plain</enum>
                  </property>
                  <property name="verticalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOff</enum>
                  </property>
                  <property name="horizontalScrollBarPolicy">
                   <enum>Qt::ScrollBarAlwaysOn</enum>
                  </property>
                  <property name="sizeAdjustPolicy">
                   <enum>QAbstractScrollArea::AdjustToContents</enum>
                  </property>
                  <property name="widgetResizable">
                   <bool>true</bool>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
                  </property>
                  <widget class="QWidget" name="scrollAreaContainer">
                   <property name="geometry">
                    <rect>
                     <x>0</x>
                     <y>0</y>
                     <width>986</width>
                     <height>381</height>
                    </rect>
                   </property>
                   <layout class="QHBoxLayout" name="horizontalLayout_16">
                    <property name="spacing">
                     <number>15</number>
                    </property>
                    <property name="topMargin">
                     <number>20</number>
                    </property>
                    <property name="rightMargin">
                     <number>20</number>
                    </property>
                   </layout>
                  </widget>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QPushButton" name="stop_notification">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>40</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>45</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>Sakkal Majalla</family>
                 <pointsize>18</pointsize>
                 <weight>75</weight>
                 <bold>true</bold>
                </font>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
color:white;
background-color:rgb(213, 33, 10);
border-radius: 5px;
}

QPushButton:hover{
background-color: rgb(198, 35, 20);
}

</string>
               </property>
               <property name="text">
                <string>ايقاف الاشعار الحالي</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_3">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="quran_page">
      <property name="styleSheet">
       <string notr="true">background-color: rgb(255, 255, 255);</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_34">
       <item>
        <widget class="QFrame" name="quran_main_frame">
         <property name="styleSheet">
          <string notr="true">background-color: rgb(255, 255, 255);</string>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_32">
          <item>
           <widget class="QLabel" name="quraan_coming_soon_label">
            <property name="font">
             <font>
              <family>Microsoft Uighur</family>
              <pointsize>30</pointsize>
             </font>
            </property>
            <property name="styleSheet">
             <string notr="true">color:black;</string>
            </property>
            <property name="text">
             <string>قريبا ...</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignCenter</set>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="settings_page">
      <property name="styleSheet">
       <string notr="true">#settings_page{
background-color: rgb(255, 255, 255);
}</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="rightMargin">
        <number>25</number>
       </property>
       <property name="bottomMargin">
        <number>30</number>
       </property>
       <item>
        <widget class="QFrame" name="frame_10">
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_31">
          <item>
           <widget class="QFrame" name="settings_frame">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_14">
             <item>
              <widget class="QFrame" name="frame_27">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>110</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_7">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>50</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_8">
                <item>
                 <widget class="QLabel" name="masjed_time_settings_label">
                  <property name="font">
                   <font>
                    <family>MS Shell Dlg 2</family>
                    <pointsize>39</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>إعدادات المسجد والوقت</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="masged_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_9">
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <spacer name="horizontalSpacer_11">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QLineEdit" name="masjedNameInput">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>400</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>24</pointsize>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::RightToLeft</enum>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QLineEdit{
border-bottom: 2px solid black;
color:black;
background-color:white;
}</string>
                  </property>
                  <property name="locale">
                   <locale language="Arabic" country="Israel"/>
                  </property>
                  <property name="inputMask">
                   <string/>
                  </property>
                  <property name="frame">
                   <bool>false</bool>
                  </property>
                  <property name="echoMode">
                   <enum>QLineEdit::Normal</enum>
                  </property>
                  <property name="cursorPosition">
                   <number>0</number>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
                  </property>
                  <property name="placeholderText">
                   <string/>
                  </property>
                  <property name="cursorMoveStyle">
                   <enum>Qt::LogicalMoveStyle</enum>
                  </property>
                  <property name="clearButtonEnabled">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="masjed_name_text_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>32</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>اسم المسجد:</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="city_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_10">
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <spacer name="horizontalSpacer_10">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QLineEdit" name="cityInput">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>300</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>24</pointsize>
                    <weight>50</weight>
                    <bold>false</bold>
                   </font>
                  </property>
                  <property name="layoutDirection">
                   <enum>Qt::RightToLeft</enum>
                  </property>
                  <property name="autoFillBackground">
                   <bool>false</bool>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QLineEdit{
border-bottom: 2px solid black;
color:black;
background-color:white;
}</string>
                  </property>
                  <property name="locale">
                   <locale language="Arabic" country="Israel"/>
                  </property>
                  <property name="frame">
                   <bool>false</bool>
                  </property>
                  <property name="cursorPosition">
                   <number>0</number>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
                  </property>
                  <property name="dragEnabled">
                   <bool>false</bool>
                  </property>
                  <property name="readOnly">
                   <bool>false</bool>
                  </property>
                  <property name="placeholderText">
                   <string/>
                  </property>
                  <property name="cursorMoveStyle">
                   <enum>Qt::LogicalMoveStyle</enum>
                  </property>
                  <property name="clearButtonEnabled">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="city_text_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>32</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>البلد:</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="quds_diff_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_11">
                <property name="topMargin">
                 <number>11</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <spacer name="horizontalSpacer_9">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QSpinBox" name="qudsTimeDiff">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Maximum" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>160</width>
                    <height>60</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>160</width>
                    <height>60</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>17</pointsize>
                   </font>
                  </property>
                  <property name="mouseTracking">
                   <bool>false</bool>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QSpinBox {
    background-color: white;
    border: 3px solid black;
    border-radius: 7px;
color:black;
}

QSpinBox::up-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/plus.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::down-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/minimize-sign.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::up-button {
    background-color: white;
    margin: 0px;
    border: none;
	border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-bottom: 2px solid black; /* Add dividing line */
}

QSpinBox::down-button {
    background-color: white;
    margin: 0px;
    padding: 0px;
    border: none;
border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-top: 2px solid black; /* Add dividing line */
}
QSpinBox::up-button:hover{
    background-color:rgb(243, 255, 255);
}

/* Hover effect for down button */
QSpinBox::down-button:hover{
    background-color:rgb(243, 255, 255);
}
</string>
                  </property>
                  <property name="wrapping">
                   <bool>false</bool>
                  </property>
                  <property name="frame">
                   <bool>true</bool>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignCenter</set>
                  </property>
                  <property name="buttonSymbols">
                   <enum>QAbstractSpinBox::PlusMinus</enum>
                  </property>
                  <property name="accelerated">
                   <bool>true</bool>
                  </property>
                  <property name="keyboardTracking">
                   <bool>false</bool>
                  </property>
                  <property name="showGroupSeparator" stdset="0">
                   <bool>false</bool>
                  </property>
                  <property name="suffix">
                   <string> min</string>
                  </property>
                  <property name="prefix">
                   <string/>
                  </property>
                  <property name="minimum">
                   <number>-300</number>
                  </property>
                  <property name="maximum">
                   <number>200</number>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="quds_diff_text_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>32</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>فارق التوقيت عن مدينة القدس : </string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_26">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>50</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="time_buttons_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>150</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_15">
                <property name="spacing">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QLabel" name="sum_win_timing_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>32</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>التوقيت:</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="win_sum_frame">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_12">
                   <property name="spacing">
                    <number>4</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>30</number>
                   </property>
                   <item>
                    <spacer name="horizontalSpacer_7">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QLabel" name="summer_settings_label">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft Uighur</family>
                       <pointsize>28</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>صيفي</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="summerButton">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton{
border: 2px solid black;
border-radius:20px;
	background-color: rgb(247, 255, 151);
}

QPushButton:hover{
border: 4px solid black;
}

QPushButton:checked{
border: 4px solid black;
}</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_8">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Preferred</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QLabel" name="winter_settings_label">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft Uighur</family>
                       <pointsize>28</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>شتوي</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="winterButton">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton{
border: 2px solid black;
border-radius:20px;
	background-color: rgb(92, 206, 255);
}

QPushButton:hover{
border: 4px solid black;
}

QPushButton:checked{
border: 4px solid black;
}</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="time_format_text_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>32</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="text">
                   <string>عرض الوقت:</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QFrame" name="time_formate_frame">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_13">
                   <property name="spacing">
                    <number>4</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>30</number>
                   </property>
                   <item>
                    <spacer name="horizontalSpacer_22">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QLabel" name="time_formate_12_label">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft Uighur</family>
                       <pointsize>28</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>12 ساعة</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="hours_12_button">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton{
border: 2px solid black;
border-radius:20px;
	background-color: rgb(138, 144, 255);
}

QPushButton:hover{
border: 4px solid black;
}

QPushButton:checked{
border: 4px solid black;
}</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                     <property name="checked">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_23">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeType">
                      <enum>QSizePolicy::Preferred</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QLabel" name="time_formate_24_label">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="font">
                      <font>
                       <family>Microsoft Uighur</family>
                       <pointsize>28</pointsize>
                      </font>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">color:black;</string>
                     </property>
                     <property name="text">
                      <string>24 ساعة</string>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QPushButton" name="hours_24_button">
                     <property name="minimumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>40</width>
                       <height>40</height>
                      </size>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">QPushButton{
border: 2px solid black;
border-radius:20px;
	background-color: rgb(138, 144, 255);
}

QPushButton:hover{
border: 4px solid black;
}

QPushButton:checked{
border: 4px solid black;
}</string>
                     </property>
                     <property name="text">
                      <string/>
                     </property>
                     <property name="checkable">
                      <bool>true</bool>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QCheckBox" name="pre_adan_sound_checkbox">
               <property name="font">
                <font>
                 <family>Microsoft Uighur</family>
                 <pointsize>28</pointsize>
                </font>
               </property>
               <property name="layoutDirection">
                <enum>Qt::RightToLeft</enum>
               </property>
               <property name="styleSheet">
                <string notr="true">QCheckBox::indicator {
    width: 30px;
    height: 30px;
    border: 1px solid gray;
    background-color: white;
}

QCheckBox::indicator:checked {
    image: none;
    background-color: white;
    border: 2px solid black;
}

QCheckBox::indicator:checked {
    image: url(:/newPrefix/images/check_mark.png); 
}</string>
               </property>
               <property name="text">
                <string>جهاز مستشعر الصوت</string>
               </property>
               <property name="iconSize">
                <size>
                 <width>80</width>
                 <height>80</height>
                </size>
               </property>
               <property name="checked">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_25">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>60</height>
                </size>
               </property>
               <property name="font">
                <font>
                 <family>Montserrat Subrayada</family>
                </font>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_39">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_35">
                <item>
                 <widget class="QLabel" name="advanced_settings_label">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="font">
                   <font>
                    <pointsize>39</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="text">
                   <string>متقدم</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_40">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_36">
                <item>
                 <spacer name="horizontalSpacer_30">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QPushButton" name="connect_to_zigbee_btn">
                  <property name="minimumSize">
                   <size>
                    <width>150</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>150</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Sakkal Majalla</family>
                    <pointsize>19</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">QPushButton{
background-color: rgb(38, 38, 38);
border-radius: 25px;
color:white;
}

QPushButton:hover{
background-color: rgb(255, 255, 255);
color:black;
border:2px solid black;
}
</string>
                  </property>
                  <property name="text">
                   <string>ارتبط</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="zigbee_connect_text_label">
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>28</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>للارتباط بجهاز الصوت zigbee </string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="new_notification_page">
      <property name="styleSheet">
       <string notr="true">#new_notification_page{
background-color:rgb(255,255,255);
}
</string>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_20">
       <item>
        <widget class="QFrame" name="frame_11">
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <property name="frameShadow">
          <enum>QFrame::Raised</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_32">
          <item>
           <widget class="QFrame" name="frame_2">
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Raised</enum>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_27">
             <item>
              <widget class="QFrame" name="frame_31">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>180</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="noti_kind_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
background-color: rgb(167, 25, 35);;
border-radius: 19px;
border: 1px solid  rgb(97, 99, 100);
}

QPushButton:hover{
background-color: rgb(180, 239, 255);
}

QPushButton:checked{
	background-color: rgb(8, 185, 123);
}</string>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_19">
                <property name="topMargin">
                 <number>20</number>
                </property>
                <property name="rightMargin">
                 <number>33</number>
                </property>
                <item>
                 <spacer name="horizontalSpacer_27">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>40</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QLabel" name="once_noti_label">
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>26</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>مرة واحدة</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="once_noti">
                  <property name="minimumSize">
                   <size>
                    <width>38</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>38</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="../resource_file.qrc">
                    <normaloff>:/newPrefix/images/button_88647.png</normaloff>:/newPrefix/images/button_88647.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>38</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                  <property name="checked">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_28">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeType">
                   <enum>QSizePolicy::Maximum</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>60</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
                <item>
                 <widget class="QLabel" name="permenant_noti_label">
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>26</pointsize>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>ثابت</string>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="permenante_noti">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>38</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>38</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="sizeIncrement">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="styleSheet">
                   <string notr="true"/>
                  </property>
                  <property name="text">
                   <string/>
                  </property>
                  <property name="icon">
                   <iconset resource="../resource_file.qrc">
                    <normaloff>:/newPrefix/images/button_88647.png</normaloff>:/newPrefix/images/button_88647.png</iconset>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>38</width>
                    <height>38</height>
                   </size>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                  <property name="checked">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QLabel" name="noti_type_text_label">
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>32</pointsize>
                    <weight>75</weight>
                    <bold>true</bold>
                   </font>
                  </property>
                  <property name="styleSheet">
                   <string notr="true">color:black;</string>
                  </property>
                  <property name="text">
                   <string>نوع الاشعار :</string>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_33">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>100</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="noti_settings_frame">
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_20">
                <item>
                 <widget class="QFrame" name="date_frame">
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <property name="frameShape">
                   <enum>QFrame::NoFrame</enum>
                  </property>
                  <property name="frameShadow">
                   <enum>QFrame::Raised</enum>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_23">
                   <item>
                    <spacer name="horizontalSpacer_29">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>40</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="QFrame" name="noti_date_frame">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="styleSheet">
                      <string notr="true">
QPushButton{
background-color: rgb(212, 212, 212);
border-radius: 16px;
border: 1px solid  rgb(97, 99, 100);
}

QPushButton:hover{
background-color: rgb(180, 239, 255);
}

QPushButton:checked{
	
	background-color: rgb(143, 216, 255);
}</string>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_26">
                      <property name="spacing">
                       <number>0</number>
                      </property>
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item>
                       <widget class="QFrame" name="frame_9">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="minimumSize">
                         <size>
                          <width>380</width>
                          <height>0</height>
                         </size>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_38">
                         <property name="spacing">
                          <number>22</number>
                         </property>
                         <property name="leftMargin">
                          <number>0</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="rightMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QLabel" name="date_noti_label">
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>28</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">color:black;</string>
                           </property>
                           <property name="text">
                            <string>اختيار التاريخ :</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QCalendarWidget" name="noti_date">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>330</height>
                            </size>
                           </property>
                           <property name="gridVisible">
                            <bool>true</bool>
                           </property>
                           <property name="verticalHeaderFormat">
                            <enum>QCalendarWidget::NoVerticalHeader</enum>
                           </property>
                           <property name="navigationBarVisible">
                            <bool>true</bool>
                           </property>
                           <property name="dateEditEnabled">
                            <bool>true</bool>
                           </property>
                           <property name="dateEditAcceptDelay">
                            <number>1500</number>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                   <item>
                    <widget class="QFrame" name="frame_32">
                     <property name="sizePolicy">
                      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
                       <horstretch>0</horstretch>
                       <verstretch>0</verstretch>
                      </sizepolicy>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>100</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="QFrame" name="time_frame_2">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>600</width>
                       <height>16777215</height>
                      </size>
                     </property>
                     <property name="frameShape">
                      <enum>QFrame::NoFrame</enum>
                     </property>
                     <property name="frameShadow">
                      <enum>QFrame::Raised</enum>
                     </property>
                     <layout class="QVBoxLayout" name="verticalLayout_22">
                      <property name="topMargin">
                       <number>0</number>
                      </property>
                      <property name="bottomMargin">
                       <number>0</number>
                      </property>
                      <item>
                       <widget class="QFrame" name="before_adan_frame">
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_23">
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>15</number>
                         </property>
                         <item>
                          <widget class="QFrame" name="frame_6">
                           <property name="frameShape">
                            <enum>QFrame::NoFrame</enum>
                           </property>
                           <property name="frameShadow">
                            <enum>QFrame::Raised</enum>
                           </property>
                           <layout class="QHBoxLayout" name="horizontalLayout_24">
                            <property name="leftMargin">
                             <number>11</number>
                            </property>
                            <property name="topMargin">
                             <number>0</number>
                            </property>
                            <property name="rightMargin">
                             <number>11</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="before_adan_noti_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>28</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::NoFrame</enum>
                              </property>
                              <property name="text">
                               <string>تعيين الاشعار قبل وقت الاذان :</string>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="before_adan_type_button">
                              <property name="minimumSize">
                               <size>
                                <width>32</width>
                                <height>32</height>
                               </size>
                              </property>
                              <property name="maximumSize">
                               <size>
                                <width>32</width>
                                <height>32</height>
                               </size>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QPushButton{
background-color:rgb(227, 227, 227);
border: none;
border-radius:16px;
}

QPushButton:hover{
background-color: rgb(225, 239, 240);
}

QPushButton:checked{
background-color: rgb(8, 185, 123);
	background-color: rgb(190, 254, 255);
}</string>
                              </property>
                              <property name="text">
                               <string/>
                              </property>
                              <property name="icon">
                               <iconset resource="../resource_file.qrc">
                                <normaloff>:/newPrefix/images/stop-button_73880.png</normaloff>:/newPrefix/images/stop-button_73880.png</iconset>
                              </property>
                              <property name="iconSize">
                               <size>
                                <width>32</width>
                                <height>32</height>
                               </size>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                              <property name="checked">
                               <bool>true</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QFrame" name="adan_min_frame">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="frameShape">
                            <enum>QFrame::NoFrame</enum>
                           </property>
                           <property name="frameShadow">
                            <enum>QFrame::Raised</enum>
                           </property>
                           <layout class="QHBoxLayout" name="horizontalLayout_21">
                            <property name="spacing">
                             <number>10</number>
                            </property>
                            <item>
                             <widget class="QSpinBox" name="before_adan_minutes_spin">
                              <property name="minimumSize">
                               <size>
                                <width>200</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="maximumSize">
                               <size>
                                <width>16777215</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>20</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QSpinBox {
	color:black;
    background-color: white;
    border: 3px solid black;
    border-radius: 7px;
}

QSpinBox::up-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/plus.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::down-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/minimize-sign.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::up-button {
    background-color: white;
    margin: 0px;
    border: none;
	border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-bottom: 2px solid black; /* Add dividing line */
}

QSpinBox::down-button {
    background-color: white;
    margin: 0px;
    padding: 0px;
    border: none;
border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-top: 2px solid black; /* Add dividing line */
}
QSpinBox::up-button:hover{
    background-color:rgb(243, 255, 255);
}

/* Hover effect for down button */
QSpinBox::down-button:hover{
    background-color:rgb(243, 255, 255);
}
</string>
                              </property>
                              <property name="frame">
                               <bool>false</bool>
                              </property>
                              <property name="buttonSymbols">
                               <enum>QAbstractSpinBox::PlusMinus</enum>
                              </property>
                              <property name="accelerated">
                               <bool>true</bool>
                              </property>
                              <property name="keyboardTracking">
                               <bool>true</bool>
                              </property>
                              <property name="suffix">
                               <string> دقائق</string>
                              </property>
                              <property name="prefix">
                               <string>ب </string>
                              </property>
                              <property name="minimum">
                               <number>1</number>
                              </property>
                              <property name="value">
                               <number>1</number>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QComboBox" name="before_adan_box">
                              <property name="minimumSize">
                               <size>
                                <width>119</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="maximumSize">
                               <size>
                                <width>16777215</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="font">
                               <font>
                                <family>Sakkal Majalla</family>
                                <pointsize>20</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QComboBox {
                border: 3px solid rgb(38, 38, 38);
				border-radius: 3px;
                padding: 1px 10px 1px 7px;
                min-width: 6em;
            }
QComboBox::drop-down {
                color:white;
                width: 26px;
                border-left-width: 1px;
                border-left-color: rgb(38, 38, 38);
                border-left-style: solid;

               background-color: rgb(38,38,38);
}
QComboBox::down-arrow {

				width:20px;
				height: 20px;
				margin: 1px 3px 1px 3px;
                image: url(:/newPrefix/images/down-chevron.png); /* Use the resource path */
}

QComboBox QAbstractItemView {
    selection-background-color: rgb(53,53,53); 
}
QComboBox QAbstractItemView::item:hover {
    background-color:rgb(53,53,53);
}

QComboBox QAbstractItemView::item {
    text-decoration: none;  /* Prevent line-through text */
    background-color: transparent;
}


           

</string>
                              </property>
                              <property name="frame">
                               <bool>true</bool>
                              </property>
                              <item>
                               <property name="text">
                                <string>قبل اذان الفجر </string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>قبل اذان الظهر </string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>قبل اذان العصر </string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>قبل اذان المغرب </string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>قبل اذان العشاء </string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>قبل اذان الجمعة</string>
                               </property>
                              </item>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="after_adan_frame">
                        <property name="sizePolicy">
                         <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                          <horstretch>0</horstretch>
                          <verstretch>0</verstretch>
                         </sizepolicy>
                        </property>
                        <property name="styleSheet">
                         <string notr="true"/>
                        </property>
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_24">
                         <property name="spacing">
                          <number>17</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>15</number>
                         </property>
                         <item>
                          <widget class="QFrame" name="frame_8">
                           <property name="frameShape">
                            <enum>QFrame::NoFrame</enum>
                           </property>
                           <property name="frameShadow">
                            <enum>QFrame::Raised</enum>
                           </property>
                           <layout class="QHBoxLayout" name="horizontalLayout_25">
                            <property name="topMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QLabel" name="after_adan_noti_label">
                              <property name="sizePolicy">
                               <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                                <horstretch>0</horstretch>
                                <verstretch>0</verstretch>
                               </sizepolicy>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>28</pointsize>
                                <weight>75</weight>
                                <bold>true</bold>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">color:black;</string>
                              </property>
                              <property name="frameShape">
                               <enum>QFrame::NoFrame</enum>
                              </property>
                              <property name="text">
                               <string>تعيين الاشعار بعد وقت الاذان :</string>
                              </property>
                              <property name="wordWrap">
                               <bool>false</bool>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QPushButton" name="after_adan_type_button">
                              <property name="minimumSize">
                               <size>
                                <width>32</width>
                                <height>32</height>
                               </size>
                              </property>
                              <property name="maximumSize">
                               <size>
                                <width>32</width>
                                <height>32</height>
                               </size>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QPushButton{
background-color:rgb(227, 227, 227);
border: none;
border-radius:16px;
}

QPushButton:hover{
background-color: rgb(225, 239, 240);
}

QPushButton:checked{
background-color: rgb(190, 254, 255);
}</string>
                              </property>
                              <property name="text">
                               <string/>
                              </property>
                              <property name="icon">
                               <iconset resource="../resource_file.qrc">
                                <normaloff>:/newPrefix/images/stop-button_73880.png</normaloff>:/newPrefix/images/stop-button_73880.png</iconset>
                              </property>
                              <property name="iconSize">
                               <size>
                                <width>32</width>
                                <height>32</height>
                               </size>
                              </property>
                              <property name="checkable">
                               <bool>true</bool>
                              </property>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                         <item>
                          <widget class="QFrame" name="frame_4">
                           <property name="frameShape">
                            <enum>QFrame::NoFrame</enum>
                           </property>
                           <property name="frameShadow">
                            <enum>QFrame::Raised</enum>
                           </property>
                           <layout class="QHBoxLayout" name="horizontalLayout_26">
                            <property name="topMargin">
                             <number>0</number>
                            </property>
                            <property name="bottomMargin">
                             <number>0</number>
                            </property>
                            <item>
                             <widget class="QSpinBox" name="after_adan_minutes_spin">
                              <property name="minimumSize">
                               <size>
                                <width>200</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="maximumSize">
                               <size>
                                <width>16777215</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="font">
                               <font>
                                <family>Microsoft Uighur</family>
                                <pointsize>20</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QSpinBox {
    background-color: white;
    border: 3px solid black;
    border-radius: 7px;
color:black;
}

QSpinBox::up-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/plus.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::down-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/minimize-sign.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::up-button {
    background-color: white;
    margin: 0px;
    border: none;
	border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-bottom: 2px solid black; /* Add dividing line */
}

QSpinBox::down-button {
    background-color: white;
    margin: 0px;
    padding: 0px;
    border: none;
border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-top: 2px solid black; /* Add dividing line */
}
QSpinBox::up-button:hover{
    background-color:rgb(243, 255, 255);
}

/* Hover effect for down button */
QSpinBox::down-button:hover{
    background-color:rgb(243, 255, 255);
}
</string>
                              </property>
                              <property name="frame">
                               <bool>false</bool>
                              </property>
                              <property name="buttonSymbols">
                               <enum>QAbstractSpinBox::PlusMinus</enum>
                              </property>
                              <property name="accelerated">
                               <bool>true</bool>
                              </property>
                              <property name="keyboardTracking">
                               <bool>false</bool>
                              </property>
                              <property name="suffix">
                               <string> ثواني</string>
                              </property>
                              <property name="prefix">
                               <string>ب </string>
                              </property>
                              <property name="minimum">
                               <number>10</number>
                              </property>
                              <property name="maximum">
                               <number>60</number>
                              </property>
                              <property name="singleStep">
                               <number>10</number>
                              </property>
                             </widget>
                            </item>
                            <item>
                             <widget class="QComboBox" name="after_adan_box">
                              <property name="minimumSize">
                               <size>
                                <width>119</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="maximumSize">
                               <size>
                                <width>16777215</width>
                                <height>60</height>
                               </size>
                              </property>
                              <property name="font">
                               <font>
                                <family>Sakkal Majalla</family>
                                <pointsize>20</pointsize>
                               </font>
                              </property>
                              <property name="styleSheet">
                               <string notr="true">QComboBox {
                border: 3px solid rgb(38, 38, 38);
				border-radius: 3px;
                padding: 1px 10px 1px 7px;
                min-width: 6em;
            }
QComboBox::drop-down {
                color:white;
                width: 26px;
                border-left-width: 1px;
                border-left-color: rgb(38, 38, 38);
                border-left-style: solid;

               background-color: rgb(38,38,38);
}


QComboBox::down-arrow {

				width:20px;
				height: 20px;
				margin: 1px 3px 1px 3px;
                image: url(:/newPrefix/images/down-chevron.png); 
}

QComboBox QAbstractItemView {
    selection-background-color: rgb(53,53,53); 
}
QComboBox QAbstractItemView::item:hover {
    background-color:rgb(53,53,53);

}
   
QComboBox QAbstractItemView::item {
    text-decoration: none;  
    background-color: transparent;
}

</string>
                              </property>
                              <item>
                               <property name="text">
                                <string>بعد اذان الفجر</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>بعد اذان الظهر</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>بعد اذان العصر</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>بعد اذان المغرب</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>بعد اذان العشاء</string>
                               </property>
                              </item>
                              <item>
                               <property name="text">
                                <string>بعد اذان الجمعة</string>
                               </property>
                              </item>
                             </widget>
                            </item>
                           </layout>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                      <item>
                       <widget class="QFrame" name="frame_3">
                        <property name="frameShape">
                         <enum>QFrame::NoFrame</enum>
                        </property>
                        <property name="frameShadow">
                         <enum>QFrame::Raised</enum>
                        </property>
                        <layout class="QVBoxLayout" name="verticalLayout_25">
                         <property name="spacing">
                          <number>17</number>
                         </property>
                         <property name="topMargin">
                          <number>0</number>
                         </property>
                         <property name="bottomMargin">
                          <number>0</number>
                         </property>
                         <item>
                          <widget class="QLabel" name="duration_label">
                           <property name="sizePolicy">
                            <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                             <horstretch>0</horstretch>
                             <verstretch>0</verstretch>
                            </sizepolicy>
                           </property>
                           <property name="font">
                            <font>
                             <family>Microsoft Uighur</family>
                             <pointsize>28</pointsize>
                             <weight>75</weight>
                             <bold>true</bold>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">color:black;</string>
                           </property>
                           <property name="text">
                            <string>مدة التشغيل (اختياري) :</string>
                           </property>
                          </widget>
                         </item>
                         <item>
                          <widget class="QSpinBox" name="duration_spinbox">
                           <property name="minimumSize">
                            <size>
                             <width>0</width>
                             <height>60</height>
                            </size>
                           </property>
                           <property name="maximumSize">
                            <size>
                             <width>16777215</width>
                             <height>60</height>
                            </size>
                           </property>
                           <property name="font">
                            <font>
                             <family>Sakkal Majalla</family>
                             <pointsize>20</pointsize>
                            </font>
                           </property>
                           <property name="styleSheet">
                            <string notr="true">QSpinBox {
color:black;
    background-color: white;
    border: 3px solid black;
    border-radius: 7px;
}

QSpinBox::up-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/plus.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::down-arrow {
    background-color: transparent;
    image: url(:/newPrefix/images/minimize-sign.png);
    width: 25px; /* Adjust based on your image size */
    height: 25px;
    border: none; /* Remove extra borders */
}

QSpinBox::up-button {
    background-color: white;
    margin: 0px;
    border: none;
	border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-bottom: 2px solid black; /* Add dividing line */
}

QSpinBox::down-button {
    background-color: white;
    margin: 0px;
    padding: 0px;
    border: none;
border-left: 3px solid Black;
    width: 28px;
    height: 28px;
    border-top: 2px solid black; /* Add dividing line */
}
QSpinBox::up-button:hover{
    background-color:rgb(243, 255, 255);
}

/* Hover effect for down button */
QSpinBox::down-button:hover{
    background-color:rgb(243, 255, 255);
}
</string>
                           </property>
                           <property name="frame">
                            <bool>false</bool>
                           </property>
                           <property name="alignment">
                            <set>Qt::AlignCenter</set>
                           </property>
                           <property name="buttonSymbols">
                            <enum>QAbstractSpinBox::PlusMinus</enum>
                           </property>
                           <property name="accelerated">
                            <bool>true</bool>
                           </property>
                           <property name="suffix">
                            <string>  دقائق</string>
                           </property>
                           <property name="minimum">
                            <number>0</number>
                           </property>
                           <property name="maximum">
                            <number>100</number>
                           </property>
                           <property name="singleStep">
                            <number>1</number>
                           </property>
                           <property name="value">
                            <number>0</number>
                           </property>
                          </widget>
                         </item>
                        </layout>
                       </widget>
                      </item>
                     </layout>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_38">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Expanding">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>170</height>
                </size>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="save_sound_frame">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="styleSheet">
                <string notr="true">QPushButton{
background-color: rgb(53, 53, 53);
border-radius: 5px;
color:white;
}

QPushButton:hover{
background-color: rgb(255, 255, 255);
color:black;
border:2px solid black;
}

</string>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_22">
                <item>
                 <widget class="QPushButton" name="save_notification_button">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>22</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>حفظ الاشعار</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="QPushButton" name="notification_sound_button">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="sizeIncrement">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="baseSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>22</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>اختيار الملف الصوتي</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                  <property name="flat">
                   <bool>false</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="QFrame" name="frame_5">
               <property name="styleSheet">
                <string notr="true">QPushButton{
color:white;
background-color:rgb(213, 33, 10);
border-radius: 5px;
}

QPushButton:hover{
background-color: rgb(198, 35, 20);
}

</string>
               </property>
               <property name="frameShape">
                <enum>QFrame::NoFrame</enum>
               </property>
               <property name="frameShadow">
                <enum>QFrame::Raised</enum>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_28">
                <property name="topMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QPushButton" name="cancel_noti_create">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>50</height>
                   </size>
                  </property>
                  <property name="font">
                   <font>
                    <family>Microsoft Uighur</family>
                    <pointsize>22</pointsize>
                   </font>
                  </property>
                  <property name="text">
                   <string>الغاء</string>
                  </property>
                  <property name="checkable">
                   <bool>true</bool>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_8">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>40</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer_2">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../resource_file.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>openMenuButton</sender>
   <signal>clicked()</signal>
   <receiver>expandedMenu</receiver>
   <slot>show()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>251</x>
     <y>60</y>
    </hint>
    <hint type="destinationlabel">
     <x>168</x>
     <y>320</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>openMenuButton</sender>
   <signal>clicked()</signal>
   <receiver>frame_19</receiver>
   <slot>hide()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>251</x>
     <y>60</y>
    </hint>
    <hint type="destinationlabel">
     <x>248</x>
     <y>108</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>powerOffButton</sender>
   <signal>clicked()</signal>
   <receiver>expandedMenu</receiver>
   <slot>hide()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>114</x>
     <y>1161</y>
    </hint>
    <hint type="destinationlabel">
     <x>177</x>
     <y>752</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>powerOffButton</sender>
   <signal>clicked()</signal>
   <receiver>frame_19</receiver>
   <slot>show()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>114</x>
     <y>1161</y>
    </hint>
    <hint type="destinationlabel">
     <x>211</x>
     <y>853</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
